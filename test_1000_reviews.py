#!/usr/bin/env python3
"""
Test Script for Collecting 1000 Movie Reviews
Combines multiple sources and approaches to collect substantial review data
"""

import sys
import time
import requests
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
import os
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import RAW_DATA_DIR
except ImportError:
    RAW_DATA_DIR = Path("data/raw")

class ComprehensiveReviewCollector:
    """Comprehensive review collector using multiple sources"""
    
    def __init__(self):
        self.tmdb_api_key = os.getenv('TMDB_API_KEY')
        self.tmdb_base_url = "https://api.themoviedb.org/3"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Ensure data directory exists
        RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        print("🎬 Comprehensive Review Collector Initialized")
        print(f"TMDB API: {'✅ Configured' if self.tmdb_api_key else '❌ Missing'}")
        print(f"Data Directory: {RAW_DATA_DIR}")
    
    def get_popular_movies_with_reviews(self, target_reviews=1000):
        """Get popular movies that are likely to have many reviews"""
        print(f"\n🔍 Finding movies with substantial reviews (target: {target_reviews})...")

        movies_with_reviews = []

        # Try multiple endpoints to find movies with more reviews
        endpoints = [
            ('top_rated', f"{self.tmdb_base_url}/movie/top_rated"),
            ('popular', f"{self.tmdb_base_url}/movie/popular"),
            ('now_playing', f"{self.tmdb_base_url}/movie/now_playing"),
        ]

        for endpoint_name, base_url in endpoints:
            print(f"\n🔍 Checking {endpoint_name} movies...")
            page = 1
            max_pages = 10

            while len(movies_with_reviews) < 100 and page <= max_pages:
                params = {
                    'api_key': self.tmdb_api_key,
                    'page': page,
                    'language': 'en-US'
                }

                try:
                    response = self.session.get(base_url, params=params, timeout=10)
                    response.raise_for_status()

                    data = response.json()
                    movies = data.get('results', [])

                    for movie in movies:
                        # Skip if we already have this movie
                        if any(m['tmdb_id'] == movie['id'] for m in movies_with_reviews):
                            continue

                        # Check if movie has reviews
                        review_count = self.check_movie_review_count(movie['id'])
                        if review_count > 0:
                            movie_info = {
                                'tmdb_id': movie['id'],
                                'title': movie['title'],
                                'release_date': movie.get('release_date'),
                                'vote_average': movie.get('vote_average'),
                                'vote_count': movie.get('vote_count'),
                                'popularity': movie.get('popularity'),
                                'estimated_reviews': review_count,
                                'source': endpoint_name
                            }
                            movies_with_reviews.append(movie_info)
                            print(f"  ✅ {movie['title']} - {review_count} reviews ({endpoint_name})")

                    page += 1
                    time.sleep(0.3)

                except Exception as e:
                    print(f"❌ Error fetching {endpoint_name} movies page {page}: {str(e)}")
                    break

        # Sort by estimated review count (descending)
        movies_with_reviews.sort(key=lambda x: x['estimated_reviews'], reverse=True)

        print(f"✅ Found {len(movies_with_reviews)} movies with reviews")
        return movies_with_reviews
    
    def check_movie_review_count(self, movie_id):
        """Check how many reviews a movie has"""
        url = f"{self.tmdb_base_url}/movie/{movie_id}/reviews"
        params = {
            'api_key': self.tmdb_api_key,
            'page': 1
        }
        
        try:
            response = self.session.get(url, params=params, timeout=5)
            response.raise_for_status()
            data = response.json()
            return data.get('total_results', 0)
        except:
            return 0
    
    def collect_movie_reviews(self, movie_id, movie_title, max_reviews=200):
        """Collect reviews for a specific movie"""
        print(f"\n📝 Collecting reviews for '{movie_title}'...")
        
        reviews = []
        page = 1
        max_pages = 50
        
        pbar = tqdm(total=max_reviews, desc=f"Reviews for {movie_title[:20]}")
        
        while len(reviews) < max_reviews and page <= max_pages:
            url = f"{self.tmdb_base_url}/movie/{movie_id}/reviews"
            params = {
                'api_key': self.tmdb_api_key,
                'page': page,
                'language': 'en-US'
            }
            
            try:
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                page_reviews = data.get('results', [])
                
                if not page_reviews:
                    break
                
                for review in page_reviews:
                    if len(reviews) >= max_reviews:
                        break
                    
                    review_data = {
                        'movie_id': movie_id,
                        'movie_title': movie_title,
                        'review_id': review['id'],
                        'author': review['author'],
                        'author_username': review.get('author_details', {}).get('username'),
                        'author_rating': review.get('author_details', {}).get('rating'),
                        'content': review['content'],
                        'created_at': review['created_at'],
                        'updated_at': review['updated_at'],
                        'url': review['url'],
                        'source': 'tmdb',
                        'text_length': len(review['content']),
                        'word_count': len(review['content'].split())
                    }
                    
                    reviews.append(review_data)
                    pbar.update(1)
                
                page += 1
                time.sleep(0.3)
                
            except Exception as e:
                print(f"❌ Error fetching reviews page {page}: {str(e)}")
                break
        
        pbar.close()
        print(f"✅ Collected {len(reviews)} reviews for '{movie_title}'")
        return reviews
    
    def run_1000_review_collection(self):
        """Run collection to get 1000 reviews"""
        print("🚀 Starting 1000 Review Collection")
        print("=" * 60)

        start_time = time.time()
        target_reviews = 1000

        # Step 1: Find movies with reviews
        movies = self.get_popular_movies_with_reviews(target_reviews)

        if not movies:
            print("❌ No movies with reviews found. Exiting.")
            return

        # Step 2: Collect reviews strategically
        all_reviews = []
        movies_processed = []

        # Focus on movies with the most reviews first
        print(f"\n📊 Top movies by review count:")
        for i, movie in enumerate(movies[:10]):
            print(f"  {i+1}. {movie['title']} - {movie['estimated_reviews']} reviews")

        for i, movie in enumerate(movies):
            if len(all_reviews) >= target_reviews:
                break

            remaining_reviews = target_reviews - len(all_reviews)
            # Collect more reviews from movies that have them
            max_per_movie = min(remaining_reviews, movie['estimated_reviews'], 500)

            if max_per_movie < 1:
                continue

            print(f"\n[{i+1}/{len(movies)}] Processing: {movie['title']}")
            print(f"   Target: {max_per_movie} reviews (Total so far: {len(all_reviews)})")
            print(f"   Estimated available: {movie['estimated_reviews']}")

            reviews = self.collect_movie_reviews(
                movie['tmdb_id'],
                movie['title'],
                max_per_movie
            )

            if reviews:
                all_reviews.extend(reviews)
                movies_processed.append(movie)
                print(f"   ✅ Added {len(reviews)} reviews (Total: {len(all_reviews)})")

            # Stop if we've reached our target
            if len(all_reviews) >= target_reviews:
                print(f"\n🎯 Target reached! Collected {len(all_reviews)} reviews")
                break

            time.sleep(1)  # Rate limiting

        # Step 3: Save results
        self.save_results(movies_processed, all_reviews)

        # Final summary
        end_time = time.time()
        duration = end_time - start_time

        print(f"\n🎉 Collection Complete!")
        print("=" * 60)
        print(f"⏱️  Duration: {duration:.1f} seconds")
        print(f"🎬 Movies processed: {len(movies_processed)}")
        print(f"📝 Reviews collected: {len(all_reviews)}")
        print(f"📊 Average reviews per movie: {len(all_reviews)/len(movies_processed):.1f}")

        return movies_processed, all_reviews
    
    def save_results(self, movies, reviews):
        """Save results to CSV files"""
        print(f"\n💾 Saving {len(reviews)} reviews to CSV...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save movies
        movies_df = pd.DataFrame(movies)
        movies_file = RAW_DATA_DIR / f"1000_review_movies_{timestamp}.csv"
        movies_df.to_csv(movies_file, index=False, encoding='utf-8')
        print(f"✅ Movies saved: {movies_file}")
        
        # Save reviews
        reviews_df = pd.DataFrame(reviews)
        reviews_file = RAW_DATA_DIR / f"1000_reviews_{timestamp}.csv"
        reviews_df.to_csv(reviews_file, index=False, encoding='utf-8')
        print(f"✅ Reviews saved: {reviews_file}")
        
        # Save summary
        summary = {
            'collection_date': datetime.now().isoformat(),
            'total_movies': len(movies),
            'total_reviews': len(reviews),
            'movies_file': str(movies_file),
            'reviews_file': str(reviews_file),
            'movies': [{'title': m['title'], 'review_count': len([r for r in reviews if r['movie_id'] == m['tmdb_id']])} for m in movies]
        }
        
        summary_file = RAW_DATA_DIR / f"1000_review_summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        print(f"✅ Summary saved: {summary_file}")

def main():
    """Main execution function"""
    collector = ComprehensiveReviewCollector()
    collector.run_1000_review_collection()

if __name__ == "__main__":
    main()
